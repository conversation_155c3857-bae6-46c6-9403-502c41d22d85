<script setup lang="jsx">
import { ref, onMounted, onUnmounted } from 'vue'
import { useResizeObserver } from '@vueuse/core'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      xAxisData: [],
      seriesData: [],
    }),
  },
})

const chartRef = ref(null)
let chartInstance = null

function initChart() {
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartRef.value)

  const options = {
    grid: {
      left: '10px', // 左边距
      right: '10px', // 右边距
      top: '40px', // 上边距
      bottom: '10px', // 下边距
      containLabel: true, // 确保不裁剪坐标轴标签
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      selectedMode: false, // 禁用图例点击
      data: ['次数'], // 图例名称，需与 series.name 对应
      top: 0, // 距离容器顶部位置
      left: 'left', // 水平居中
      itemHeight: 10, // 图例的高度
    },
    xAxis: {
      type: 'category',
      data: props.chartData.xAxisData,
      axisTick: {
        show: true,
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      // name: '次数',
      // nameLocation: 'end', // 坐标轴名称位置中间
      // nameGap: 20, // 与坐标轴的距离
      // nameTextStyle: {
      //   align: 'right',
      //   color: '#666',
      // },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '次数',
        type: 'line',
        data: props.chartData.seriesData,
        label: {
          show: true,
          position: 'top',
        },
      },
    ],
  }

  chartInstance.setOption(options)
}

function init() {
  initChart()
  useResizeObserver(chartRef, () => {
    chartInstance.resize()
  })
}

onMounted(() => {
  init()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<template>
  <div class="line-chart" ref="chartRef"></div>
</template>

<style lang="less" scoped>
.line-chart {
  height: 100%;
}
</style>
