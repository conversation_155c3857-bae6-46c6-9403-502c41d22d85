import { cloneDeep, isBoolean } from 'lodash'
import { reactive, ref } from 'vue'

export function useForm(initFormData = {}, initFormRules = {}) {
  const rowData = cloneDeep(initFormData)

  const form = ref({ ...initFormData })
  const rules = reactive({ ...initFormRules })
  const formRef = ref(null)

  const validateForm = async (fields) => {
    if (!formRef.value) {
      return false
    }
    const valid = await formRef.value.validate(fields)
    return isBoolean(valid) ? valid : false
  }

  const resetForm = () => {
    resetData()
    if (formRef.value) {
      formRef.value.reset()
    }
  }

  const resetData = () => {
    form.value = cloneDeep(rowData)
  }

  const clearValidate = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  return {
    form,
    rules,
    formRef,
    validateForm,
    resetForm,
    clearValidate,
    resetData,
  }
}
