<script setup>
import { computed } from 'vue'
import emitter, { EVENT_NAMES } from '@/utils/emitter'

const props = defineProps({
  data: Object,
})

// hover-activate
const isActive = computed(() => props.data.states?.includes('active'))

function handleNodeClick() {
  emitter.emit(EVENT_NAMES.NODE_MORE_CLICK, props.data)
}
</script>

<template>
  <div class="job-node-more" :class="{ active: isActive }" @click="handleNodeClick">
    <span class="job-node-more-label">{{ props.data.label }}</span>
  </div>
</template>

<style lang="less" scoped>
.job-node-more {
  width: 40px;
  height: 40px;
  color: #333;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ccc;
  .job-node-more-label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.active {
  color: #fff;
  background: #6696f6;
}
</style>
