<script setup>
import { computed } from 'vue'
import HeadMenu from '@/components/HeadMenu.vue'

const avatar = computed(() => 'https://rhrc.woa.com/photo/150/yiwuhe.png')

function handleLogout() {}
</script>

<template>
  <div class="trace-lineage-app">
    <head-menu @logout="handleLogout" :avatar="avatar">
      <template #header-left>
        <h1 class="trace-lineage-title">数 据 血 缘</h1>
      </template>
    </head-menu>
    <div class="trace-lineage-page-container">
      <router-view />
    </div>
  </div>
</template>

<style scoped lang="less">
.trace-lineage-app {
  background-color: #ebf0f7;
  height: 100%;
  .trace-lineage-title {
    display: flex;
    line-height: 60px;
    color: #fff;
    align-items: center;
    height: 60px;
  }
  .trace-lineage-page-container {
    height: calc(~'100% - 60px');
    overflow: auto;
  }
}
</style>
