<script setup lang="jsx">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const form = ref({
  fieldName: '',
  type: '',
  updateTime: [],
})

const tableData = ref([
  {
    id: 1,
    fieldName: '字段名1',
    description: '描述1',
    datasource: '数据源1',
    updateTime: '2023-01-01 00:00:00',
  },
  {
    id: 2,
    fieldName: '字段名2',
    description: '描述1',
    datasource: '数据源2',
    updateTime: '2023-01-01 00:00:00',
  },
])

const columns = ref([
  {
    title: '序号',
    colKey: 'serial-number',
    width: 60,
  },
  {
    title: '字段名',
    colKey: 'fieldName',
  },
  {
    title: '字段类型',
    colKey: 'type',
  },
  {
    title: '描述',
    colKey: 'description',
  },
  {
    title: '所属数据表',
    colKey: 'datasource',
  },
  {
    title: '最近更新时间',
    colKey: 'updateTime',
  },
  {
    title: '操作',
    colKey: 'operation',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleTrace(row)}>
          血缘
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleUpdate(row)}>
          更新
        </t-link>
      </t-space>
    ),
  },
])

function handleTrace(row) {
  console.log(row)
  router.push({ name: 'fieldTrace' })
}

function handleUpdate(row) {
  console.log(row)
}

const pagination = ref({
  defaultPageSize: 10,
  total: 100,
  showJumper: true,
  showPageSize: true,
  showTotal: true,
})
</script>

<template>
  <div class="trace-lineage-field-page">
    <div class="serach-wrap">
      <t-form :data="form" layout="inline" labelAlign="left" labelWidth="10">
        <t-form-item label="字段名">
          <t-input v-model="form.fieldName"></t-input>
        </t-form-item>
        <t-form-item label="字段类型">
          <t-select v-model="form.type" :options="[]"></t-select>
        </t-form-item>
        <t-form-item label="更新时间">
          <t-date-range-picker
            v-model="form.updateTime"
            enable-time-picker
            allow-input
            clearable
            :placeholder="['开始时间', '结束时间']"
          />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button variant="outline">查询</t-button>
            <t-button variant="outline">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>

    <t-table
      row-key="id"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      max-height="100%"
    ></t-table>
  </div>
</template>

<style lang="less" scoped>
.trace-lineage-field-page {
  height: 100%;
  .serach-wrap {
    margin-bottom: var(--trace-lineage-margin);
    :deep(.t-form__item) {
      &:not(.t-form__item-with-extra) {
        margin-bottom: 0;
      }
    }
  }
  :deep(.t-table) {
    height: calc(100% - 32px - var(--trace-lineage-margin));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
