import { useDebounceFn } from '@vueuse/core'

export function useFilter({ resetForm, setPageCurrent, fetchData }) {
  // 触发请求的方法
  function refresh() {
    if (!fetchData) return
    fetchData()
  }

  // 用于重置按钮
  function onReset() {
    resetForm() // 重置表单到初始值
    setPageCurrent(1)
    refresh()
  }

  // 用于搜索按钮
  const onSearch = useDebounceFn(() => {
    setPageCurrent(1)
    refresh()
  }, 500)

  return {
    refresh, // 手动刷新（可用于“刷新”按钮）
    onSearch, // 搜索时调用
    onReset, // 重置时调用
  }
}
