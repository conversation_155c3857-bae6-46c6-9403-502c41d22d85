<script setup lang="jsx">
import { onMounted, onUnmounted, ref } from 'vue'
import { RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { useG6Graph } from '@/hooks'
import { treeToGraphData } from '@antv/g6'
import emitter, { EVENT_NAMES } from '@/utils/emitter'
import JobNode from '@/components/JobNode.vue'
import JobNodeMore from '@/components/JobNodeMore.vue'
// 节点信息
import TableDetail from '../components/TableDetail.vue'
import DatasourceDetail from '../components/DatasourceDetail.vue'
import ModelDetail from '../components/ModelDetail.vue'
import FieldDetail from '../components/FieldDetail.vue'
import InterfaceDetail from '../components/InterfaceDetail.vue'
import ApplicationDetail from '../components/ApplicationDetail.vue'

// 原始数据，用于维护完整的树结构
let rawData = {
  id: 'Modeling Methods',
  data: {
    type: 'datasource',
  },
  children: [
    {
      id: 'model-1',
      data: {
        type: 'model',
      },
      children: [
        {
          id: 'upstream-table',
          data: {
            type: 'upstream-table',
          },
          children: [
            {
              id: 'target-table1',
              data: {
                type: 'target-table',
              },
              children: [
                {
                  id: 'downstream-interface 1',
                  data: {
                    type: 'downstream-interface',
                  },
                },
                {
                  id: 'downstream-interface 2',
                  data: {
                    type: 'downstream-interface',
                  },
                },
                {
                  id: 'downstream-interface 3',
                  data: {
                    type: 'downstream-interface',
                  },
                },
                {
                  id: 'downstream-interface 4',
                  data: {
                    type: 'downstream-interface',
                  },
                },
              ],
            },
            {
              id: 'target-table 2',
              data: {
                type: 'target-table',
              },
            },
            {
              id: 'target-table 3',
              data: {
                type: 'target-table',
              },
            },
          ],
        },
        {
          id: 'upstream-table 2',
          data: {
            type: 'upstream-table',
          },
        },
        {
          id: 'upstream-table 3',
          data: {
            type: 'upstream-table',
          },
        },
      ],
    },
    {
      id: 'model-2',
      data: {
        type: 'model',
      },
    },
    {
      id: 'model-3',
      data: {
        type: 'model',
      },
    },
  ],
}

// 在树结构中根据ID查找节点（用于数据修改）
const findNodeInTree = (tree, targetId) => {
  if (tree.id === targetId) return tree
  if (tree.children) {
    for (const child of tree.children) {
      const found = findNodeInTree(child, targetId)
      if (found) return found
    }
  }
  return null
}
// 递归处理树数据：仅对 data.type === 'downstream-interface' 且数量 > 2 的子节点进行折叠
const processTreeData = (node) => {
  // 递归处理子节点
  if (node.children) {
    node.children.forEach((child) => processTreeData(child))
  }

  // 只对包含 'downstream-interface' 类型子节点的节点进行处理
  const downstreamChildren = node.children?.filter(
    (child) => child.data?.type === 'downstream-interface',
  )

  // 如果下游接口少于等于2个，不折叠
  if (!downstreamChildren || downstreamChildren.length <= 2) {
    return
  }

  // 超过2个，进行折叠
  const visibleCount = 2
  const visibleChildren = downstreamChildren.slice(0, visibleCount)
  const hiddenChildren = downstreamChildren.slice(visibleCount)

  // 找到原 children 中非 downstream-interface 的节点（保留）
  const otherChildren = node.children.filter((child) => child.data?.type !== 'downstream-interface')

  // 新的 children = 其他类型节点 + 前2个 downstream-interface 节点
  node.children = [...otherChildren, ...visibleChildren]

  // 存储被隐藏的 downstream-interface 节点
  node.collapsedChildren = hiddenChildren

  // 添加“更多”节点
  node.children.push({
    id: `more-node-${node.id}`,
    label: `+${hiddenChildren.length}`,
    isMoreNode: true,
    parentOriginalId: node.id,
  })
}
processTreeData(rawData)
// 使用组合式函数
const { paintboardRef, graph } = useG6Graph(treeToGraphData(rawData), {
  node: {
    type: 'job-node',
    style: {
      component: (data) =>
        data.isMoreNode ? (
          <JobNodeMore data={Object.assign({}, data)} />
        ) : (
          <JobNode data={Object.assign({}, data)} />
        ),
    },
  },
})

const visible = ref(false)
const currentNodeData = ref({})
const currentNodeType = ref('')
const nodeCompMapping = {
  datasource: DatasourceDetail,
  table: TableDetail,
  field: FieldDetail,
  interface: InterfaceDetail,
  application: ApplicationDetail,
  model: ModelDetail,
}
const nodeTitleMapping = {
  datasource: '数据源详情',
  table: '表详情',
  field: '字段详情',
  interface: '接口详情',
  application: '应用详情',
  model: '数据模型详情',
}
const refMap = {}
function setRefMap(el, current) {
  if (el) {
    refMap[current] = el
  }
}

function handleNodeClick(payload) {
  currentNodeData.value = { ...payload.data, id: payload.id }
  currentNodeType.value = payload.data.type
  visible.value = true
  // nextTick(() => {
  //   refMap[currentNodeType.value]?.setData(cloneDeep(currentNodeData.value));
  // });
}
function handleNodeMoreClick(model) {
  if (model.isMoreNode && model.parentOriginalId) {
    console.log('Expanding node:', model.parentOriginalId)

    // 1. 移除“更多”节点
    graph.value.removeData([model.id])
    const res = graph.value.getNodeData()
    console.log('[ res ] >', res)
  }
}

onMounted(() => {
  emitter.on(EVENT_NAMES.NODE_CLICK, handleNodeClick)
  emitter.on(EVENT_NAMES.NODE_MORE_CLICK, handleNodeMoreClick)
})

onUnmounted(() => {
  emitter.off(EVENT_NAMES.NODE_CLICK, handleNodeClick)
  emitter.off(EVENT_NAMES.NODE_MORE_CLICK, handleNodeMoreClick)
})
</script>

<template>
  <div class="blood-relationship-page">
    <div class="paintboard-tool-left">
      <t-space size="10px">
        <t-input placeholder="应用">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="数据表">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="字段">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
      </t-space>
    </div>
    <div class="paintboard-tool-right">
      <span class="paintboard-tool-right-wrap">
        <RefreshIcon />
      </span>
    </div>
    <div ref="paintboardRef" class="paintboard-inner"></div>
  </div>
  <t-drawer v-model:visible="visible" :closeBtn="true" :footer="false" size="1056px" destroyOnClose>
    <template #header>{{ nodeTitleMapping[currentNodeType] }}</template>
    <component
      :is="nodeCompMapping[currentNodeType]"
      :data="currentNodeData"
      :ref="(el) => setRefMap(el, currentNodeType)"
    ></component>
  </t-drawer>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  position: relative;
  .paintboard-tool-left,
  .paintboard-tool-right {
    position: absolute;
    top: 10px;
    height: 32px;
    line-height: 32px;
    z-index: 1001;
  }
  .paintboard-tool-left {
    left: 10px;
  }
  .paintboard-tool-right {
    right: 10px;
    .paintboard-tool-right-wrap {
      height: 32px;
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background-color: #fff;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .paintboard-inner {
    height: 100%;
    width: 100%;
  }
}
</style>
