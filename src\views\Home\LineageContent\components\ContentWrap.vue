<script setup lang="jsx">
defineProps({
  title: String,
})
</script>

<template>
  <div class="content-wrap">
    <div class="content-title">{{ title }}</div>
    <div class="content-main">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.content-wrap {
  height: 100%;
  .content-title {
    color: #333333;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: var(--trace-lineage-margin);
  }
  .content-main {
    height: calc(100% - 22px - var(--trace-lineage-margin));
  }
}
</style>
