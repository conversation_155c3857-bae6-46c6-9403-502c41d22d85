<!-- EllipsisWithTooltip.vue -->
<template>
  <t-tooltip :disabled="!overText" :content="text" :placement="placement">
    <span
      @mouseenter="onMouseenter"
      :class="textClass"
      :style="textStyle"
      @click="textClick"
      ref="textRef"
    >
      {{ text }}
    </span>
  </t-tooltip>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue'
import { useResizeObserver } from '@vueuse/core'
import { useDebounceFn } from '@vueuse/core'

const props = defineProps({
  text: {
    type: [String, Number],
    default: '',
  },
  placement: {
    type: String,
    default: 'top',
  },
  lineClamp: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['click'])

const textRef = ref(null)

const overText = ref(false)

const textStyle = computed(() => {
  if (props.lineClamp > 1) {
    return {
      display: '-webkit-box',
      '-webkit-line-clamp': props.lineClamp,
      '-webkit-box-orient': 'vertical',
    }
  }
  return {}
})

const textClass = computed(() => {
  const classList = ['trace-lineage-ellipsis-with-tooltip__text']
  if (props.lineClamp >= 1) {
    classList.push(`trace-lineage-line-clamp-${props.lineClamp}`)
  }
  return classList
})

const textClick = () => {
  emit('click', props.text)
}

const triggerTextDebounced = useDebounceFn(() => {
  const el = textRef.value
  if (!el) return

  const isOverflowX = el.scrollWidth - el.clientWidth > 0
  const isOverflowY = el.scrollHeight - el.clientHeight > 2

  overText.value = isOverflowX || isOverflowY
}, 100)

function onMouseenter() {
  triggerTextDebounced()
}

const stopObserver = useResizeObserver(textRef, () => {
  // 尺寸变化时也检测是否溢出
  triggerTextDebounced()
})

onBeforeUnmount(() => {
  stopObserver() // 停止 observe
})
</script>

<style lang="less" scoped>
.trace-lineage-ellipsis-with-tooltip__text {
  display: inline-block;
  max-width: 100%;

  &.trace-lineage-line-clamp-1 {
    white-space: nowrap;
  }

  &[class*='trace-lineage-line-clamp-'] {
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
}
</style>
