import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'

import hrVueNext from '@tencent/hr-vue-next'
import '@tencent/hr-vue-next/es/style/index.css'

import '@/styles/index.less'

const app = createApp(App)

app.use(TDesign)
app.use(hrVueNext)
app.use(createPinia())
app.use(router)

app.mount('#app')
