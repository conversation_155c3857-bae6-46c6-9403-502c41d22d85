<script setup lang="jsx">
import { RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { useG6Graph } from '@/hooks'
import { treeToGraphData } from '@antv/g6'
import JobNode from '@/components/JobNode.vue'

const data = {
  id: 'Modeling Methods111111111111111111111111',
  data: {
    type: 'table',
  },
  children: [
    {
      id: 'Classification',
      children: [
        {
          id: 'Logistic regression',
        },
        {
          id: 'Linear discriminant analysis',
        },
        {
          id: 'Rules',
        },
        {
          id: 'Decision trees',
        },
        {
          id: 'Naive Bayes',
        },
        {
          id: 'K nearest neighbor',
        },
        {
          id: 'Probabilistic neural network',
        },
        {
          id: 'Support vector machine',
        },
      ],
    },
    {
      id: 'Consensus',
      children: [
        {
          id: 'Models diversity',
          children: [
            {
              id: 'Different initializations',
            },
            {
              id: 'Different parameter choices',
            },
            {
              id: 'Different architectures',
            },
            {
              id: 'Different modeling methods',
            },
            {
              id: 'Different training sets',
            },
            {
              id: 'Different feature sets',
            },
          ],
        },
        {
          id: 'Methods',
          children: [
            {
              id: 'Classifier selection',
            },
            {
              id: 'Classifier fusion',
            },
          ],
        },
        {
          id: 'Common',
          children: [
            {
              id: 'Bagging',
            },
            {
              id: 'Boosting',
            },
            {
              id: 'AdaBoost',
            },
          ],
        },
      ],
    },
    {
      id: 'Regression',
      children: [
        {
          id: 'Multiple linear regression',
        },
        {
          id: 'Partial least squares',
        },
        {
          id: 'Multi-layer feedforward neural network',
        },
        {
          id: 'General regression neural network',
        },
        {
          id: 'Support vector regression',
        },
      ],
    },
  ],
}
// 使用组合式函数
const { paintboardRef } = useG6Graph(treeToGraphData(data), {
  node: {
    type: 'job-node',
    style: {
      component: (data) => <JobNode data={Object.assign({}, data)} />,
    },
  },
})
</script>

<template>
  <div class="blood-relationship-page">
    <div class="paintboard-tool-left">
      <t-space size="10px">
        <t-input placeholder="应用">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="数据表">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="字段">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
      </t-space>
    </div>
    <div class="paintboard-tool-right">
      <span class="paintboard-tool-right-wrap">
        <RefreshIcon />
      </span>
    </div>
    <div ref="paintboardRef" class="paintboard-inner"></div>
  </div>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  position: relative;
  .paintboard-tool-left,
  .paintboard-tool-right {
    position: absolute;
    top: 10px;
    height: 32px;
    line-height: 32px;
    z-index: 1001;
  }
  .paintboard-tool-left {
    left: 10px;
  }
  .paintboard-tool-right {
    right: 10px;
    .paintboard-tool-right-wrap {
      height: 32px;
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background-color: #fff;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .paintboard-inner {
    height: 100%;
    width: 100%;
  }
}
</style>
