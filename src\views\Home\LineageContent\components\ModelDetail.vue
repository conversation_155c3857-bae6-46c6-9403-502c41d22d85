<script setup lang="jsx">
import { ref } from 'vue'
import BlockHeader from '@/components/BlockHeader.vue'

defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

const tableData = ref([
  {
    id: 1,
    fieldName: '字段名1',
    fieldType: '字段类型1',
    description: '描述1',
    type: '2023',
    updater: '更新人1',
  },
  {
    id: 2,
    fieldName: '字段名2',
    fieldType: '字段类型2',
    description: '描述2',
    type: '2023',
    updater: '更新人2',
  },
  {
    id: 3,
    fieldName: '字段名3',
    fieldType: '字段类型3',
    description: '描述3',
    type: '2023',
    updater: '更新人3',
  },
  {
    id: 4,
    fieldName: '字段名4',
    fieldType: '字段类型4',
    description: '描述4',
    type: '2023',
    updater: '更新人4',
  },
  {
    id: 5,
    fieldName: '字段名5',
    fieldType: '字段类型5',
    description: '描述5',
    type: '2023',
    updater: '更新人5',
  },
  {
    id: 6,
    fieldName: '字段名6',
    fieldType: '字段类型6',
    description: '描述6',
    type: '2023',
    updater: '更新人6',
  },
  {
    id: 7,
    fieldName: '字段名7',
    fieldType: '字段类型7',
    description: '描述7',
    type: '2023',
    updater: '更新人7',
  },
])

const columns = ref([
  {
    title: '序号',
    colKey: 'serial-number',
    width: 60,
  },
  {
    title: '字段编码',
    colKey: 'fieldName',
  },
  {
    title: '字段名',
    colKey: 'fieldType',
  },
  {
    title: '描述',
    colKey: 'description',
  },
  {
    title: '字段类型',
    colKey: 'type',
  },
  {
    title: '是否主键',
    colKey: 'updater',
  },
  {
    title: '是否加密',
    colKey: 'encrypt',
  },
  {
    title: '是否加盐',
    colKey: 'salt',
  },
  {
    title: '查询运算符',
    colKey: 'operate',
    width: 120,
  },
  {
    title: '基础配置',
    colKey: 'config',
  },
  {
    title: '样本数量',
    colKey: 'count',
  },
])
</script>

<template>
  <div class="model-detail-page">
    <section style="margin-bottom: 20px">
      <BlockHeader title="基础信息" style="margin-bottom: 20px"></BlockHeader>
      <t-row :gutter="[12, 12]">
        <t-col class="flex-align-center" :span="4">
          <span class="label">所属数据源</span>
          <span class="content">202502250002</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">schema</span>
          <span class="content">916619319350666</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">表名</span>
          <span class="content">sr视图任务1</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">模型名</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">模型描述</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">模型记录</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
      </t-row>
    </section>
    <section>
      <BlockHeader title="字段信息" style="margin-bottom: 20px"></BlockHeader>
      <t-table
        class="table"
        row-key="id"
        :data="tableData"
        :columns="columns"
        max-height="100%"
      ></t-table>
    </section>
  </div>
</template>

<style lang="less" scoped>
.model-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  section {
    &:last-child {
      flex: 1;
      overflow: hidden;
      .table {
        height: calc(100% - 22px - 20px);
      }
    }
  }
  .label {
    display: inline-block;
    width: 92px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~'100% - 100px');
  }
}
</style>
