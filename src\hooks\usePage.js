import { reactive } from 'vue'

export function usePage(fn, options = {}) {
  // 默认配置
  const defaultOptions = {
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    onChange: ({ current, pageSize }) => {
      pagination.current = current
      if (pagination.pageSize !== pageSize) {
        pagination.current = 1
      }
      pagination.pageSize = pageSize
      fn?.()
    },
  }

  const mergedOptions = { ...defaultOptions, ...options }

  const pagination = reactive({
    current: mergedOptions.current,
    pageSize: mergedOptions.pageSize,
    total: mergedOptions.total,
    showJumper: mergedOptions.showJumper,
    onChange: mergedOptions.onChange,
  })

  const setPageCurrent = (current) => {
    pagination.current = Math.max(1, Math.floor(current))
  }

  const setPageSize = (pageSize) => {
    pagination.pageSize = Math.max(1, Math.floor(pageSize))
  }

  const setPageTotal = (total) => {
    pagination.total = Math.max(0, Math.floor(total))
  }

  return {
    pagination, // 响应式分页对象
    setPageCurrent,
    setPageSize,
    setPageTotal,
  }
}
