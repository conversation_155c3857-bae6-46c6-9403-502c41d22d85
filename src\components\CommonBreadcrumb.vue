<template>
  <div class="trace-lineage__breadcrumb">
    <t-breadcrumb :maxItemWidth="'150'">
      <t-breadcrumbItem
        v-for="(item, index) in breadcrumbs"
        :key="index"
        @click="handleClick(index)"
      >
        {{ item }}
      </t-breadcrumbItem>
    </t-breadcrumb>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const props = defineProps({
  breadcrumbs: {
    type: Array,
    default: () => [],
    required: true,
  },
})

const router = useRouter()

function handleClick(index) {
  if (props.breadcrumbs.length === 1) return
  if (index === 0) {
    router.back()
  }
}
</script>

<style lang="less" scoped>
.trace-lineage__breadcrumb {
  padding: var(--trace-lineage-padding);
  background-color: #fff;
}
</style>
