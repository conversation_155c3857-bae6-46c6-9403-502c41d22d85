<script setup lang="jsx">
import { ref } from 'vue'
import ContentWrap from '../components/ContentWrap.vue'
import LineChart from '../components/LineChart.vue'

const form = ref({
  time: [],
  field: '',
  interface: '',
  application: '',
})

const chartData = ref({
  xAxisData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  seriesData: [150, 230, 224, 218, 135, 147, 260],
})

const interfaceData = ref([
  {
    interfaceCode: '接口1',
    count: 100,
  },
  {
    interfaceCode: '接口2',
    count: 200,
  },
  {
    interfaceCode: '接口3',
    count: 300,
  },
])

const applicationData = ref([
  {
    application: '应用1',
    count: 100,
  },
  {
    application: '应用2',
    count: 200,
  },
  {
    application: '应用3',
    count: 300,
  },
])

const interfaceColumns = [
  {
    title: '接口编码',
    colKey: 'interfaceCode',
  },
  {
    title: '调用次数',
    colKey: 'count',
  },
]

const applicationColumns = [
  {
    title: '应用编码',
    colKey: 'application',
  },
  {
    title: '调用次数',
    colKey: 'count',
  },
]
</script>

<template>
  <div class="usage-situation-page">
    <div class="filter-form">
      <t-form :data="form" layout="inline" labelAlign="left" labelWidth="10">
        <t-form-item label="日期">
          <t-date-range-picker
            v-model="form.time"
            enable-time-picker
            allow-input
            clearable
            style="width: 366px"
            :placeholder="['开始时间', '结束时间']"
          />
        </t-form-item>
        <t-form-item label="接口">
          <t-input v-model="form.interface"></t-input>
        </t-form-item>
        <t-form-item label="应用">
          <t-select v-model="form.application" :options="[]"></t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button variant="outline">查询</t-button>
            <t-button variant="outline">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
    <t-row :gutter="[20, 20]">
      <!-- 第一行，3列 -->
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="请求量">
            <LineChart :chartData="chartData"></LineChart>
          </ContentWrap>
        </div>
      </t-col>
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="接口调用排名">
            <t-table
              row-key="id"
              :columns="interfaceColumns"
              :data="interfaceData"
              style="height: 100%"
              max-height="100%"
            ></t-table>
          </ContentWrap>
        </div>
      </t-col>
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="应用调用排名">
            <t-table
              row-key="id"
              :columns="applicationColumns"
              :data="applicationData"
              style="height: 100%"
              max-height="100%"
            ></t-table>
          </ContentWrap>
        </div>
      </t-col>
    </t-row>
  </div>
</template>

<style lang="less" scoped>
.usage-situation-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  .filter-form {
    margin-bottom: 20px;
    :deep(.t-form__item) {
      &:not(.t-form__item-with-extra) {
        margin-bottom: 0;
      }
    }
  }
  :deep(.t-row) {
    height: 360px;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .t-col {
      height: 100%;
    }
    .wrap {
      height: 100%;
      border-radius: 4px;
      border: 1px solid #edeff7;
      padding: var(--trace-lineage-padding);
    }
  }
  :deep(.t-table th) {
    background: #fff;
    color: #999999;
  }
  :deep(.t-table__header--fixed:not(.t-table__header--multiple) > tr > th) {
    background: #fff;
    color: #999999;
  }
}
</style>
