<template>
  <div ref="container" class="graph-container"></div>
</template>

<script setup>
import { Graph, treeToGraphData } from '@antv/g6'
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 容器引用
const container = ref(null)

// G6 实例（避免被响应式劫持）
let graph = null
let rawData = null

// 错误处理
const handleError = (error) => {
  console.error('G6 TreeGraph Error:', error)
  // 可以在这里通知用户
}

// 判断是否为叶子节点
const isLeafNode = (d) => !d.children || d.children.length === 0
// 递归处理树数据：仅对 data.type === 'downstream-interface' 且数量 > 2 的子节点进行折叠
const processTreeData = (node) => {
  // 递归处理子节点
  if (node.children) {
    node.children.forEach((child) => processTreeData(child))
  }

  // 只对包含 'downstream-interface' 类型子节点的节点进行处理
  const downstreamChildren = node.children?.filter(
    (child) => child.data?.type === 'downstream-interface',
  )

  // 如果下游接口少于等于2个，不折叠
  if (!downstreamChildren || downstreamChildren.length <= 2) {
    return
  }

  // 超过2个，进行折叠
  const visibleCount = 2
  const visibleChildren = downstreamChildren.slice(0, visibleCount)
  const hiddenChildren = downstreamChildren.slice(visibleCount)
  console.log('[ hiddenChildren ] >', hiddenChildren)

  // 找到原 children 中非 downstream-interface 的节点（保留）
  const otherChildren = node.children.filter((child) => child.data?.type !== 'downstream-interface')

  // 新的 children = 其他类型节点 + 前2个 downstream-interface 节点
  node.children = [...otherChildren, ...visibleChildren]

  // 存储被隐藏的 downstream-interface 节点
  node.collapsedChildren = hiddenChildren

  // 添加“更多”节点
  node.children.push({
    id: `more-node-${node.id}`,
    label: `+${hiddenChildren.length}`,
    isMoreNode: true,
    parentOriginalId: node.id,
  })
}

// 在树结构中根据ID查找节点（用于数据修改）
const findNodeInTree = (tree, targetId) => {
  if (tree.id === targetId) return tree
  if (tree.children) {
    for (const child of tree.children) {
      const found = findNodeInTree(child, targetId)
      if (found) return found
    }
    return null
  }
}

// 初始化图
const initGraph = async () => {
  if (!container.value) return

  try {
    rawData = {
      id: 'Modeling Methods',
      data: {
        type: 'datasource',
      },
      children: [
        {
          id: 'model-1',
          data: {
            type: 'model',
          },
          children: [
            {
              id: 'upstream-table',
              data: {
                type: 'upstream-table',
              },
              children: [
                {
                  id: 'target-table 1',
                  data: {
                    type: 'target-table',
                  },
                  children: [
                    {
                      id: 'downstream-interface 1',
                      data: {
                        type: 'downstream-interface',
                      },
                    },
                    {
                      id: 'downstream-interface 2',
                      data: {
                        type: 'downstream-interface',
                      },
                    },
                    {
                      id: 'downstream-interface 3',
                      data: {
                        type: 'downstream-interface',
                      },
                    },
                    {
                      id: 'downstream-interface 4',
                      data: {
                        type: 'downstream-interface',
                      },
                    },
                  ],
                },
                {
                  id: 'target-table 2',
                  data: {
                    type: 'target-table',
                  },
                },
                {
                  id: 'target-table 3',
                  data: {
                    type: 'target-table',
                  },
                },
              ],
            },
            {
              id: 'upstream-table 2',
              data: {
                type: 'upstream-table',
              },
            },
            {
              id: 'upstream-table 3',
              data: {
                type: 'upstream-table',
              },
            },
          ],
        },
        {
          id: 'model-2',
          data: {
            type: 'model',
          },
        },
        {
          id: 'model-3',
          data: {
            type: 'model',
          },
        },
      ],
    }

    // 处理数据
    processTreeData(rawData)
    console.log('[ rawData ] >', rawData)
    const graphData = treeToGraphData(rawData)
    console.log('[ graphData ] >', graphData)
    // 创建图实例
    graph = new Graph({
      container: container.value,
      autoFit: 'view',
      data: graphData,
      node: {
        style: {
          labelText: (d) => d.id,
          labelPlacement: (d) => (isLeafNode(d) ? 'right' : 'left'),
          labelBackground: true,
          ports: [{ placement: 'right' }, { placement: 'left' }],
          fill: (d) => (d.isMoreNode ? '#f0f0f0' : '#C6E5FF'),
          stroke: (d) => (d.isMoreNode ? '#ccc' : '#5B8FF9'),
          lineWidth: 1,
        },
        animation: { enter: false },
      },
      edge: {
        type: 'cubic-horizontal',
        style: { stroke: '#b5b5b5', lineWidth: 1.5 },
        animation: { enter: false },
      },
      layout: {
        type: 'dendrogram',
        direction: 'LR',
        nodeSep: 36,
        rankSep: 250,
      },
      behaviors: [
        'drag-canvas',
        'zoom-canvas',
        {
          type: 'drag-element',
          enable: true,
          // 添加配置来避免事件冲突
          eventName: 'drag',
        },
        'hover-activate', // 节点hover
        'click-select', // 节点click
      ],
    })

    await graph.render()

    // 绑定点击事件
    const handleClick = (evt) => {
      const node = evt.item
      if (!node) return

      const model = node.getModel()
      if (!model) return

      if (model.isMoreNode && model.parentOriginalId) {
        console.log('Expanding node:', model.parentOriginalId)

        // 1. 移除“更多”节点
        graph.removeItem(node)

        // 2. 在原始数据中找到父节点
        const parentNodeInData = findNodeInTree(rawData, model.parentOriginalId)
        if (!parentNodeInData || !parentNodeInData.collapsedChildren) {
          console.warn('Parent node or collapsed children not found')
          return
        }

        // 3. 添加隐藏节点
        parentNodeInData.children = parentNodeInData.children || []
        parentNodeInData.collapsedChildren.forEach((child) => {
          const cleanChild = { ...child }
          delete cleanChild.isMoreNode
          delete cleanChild.parentOriginalId
          parentNodeInData.children.push(cleanChild)
        })

        // 4. 清理
        delete parentNodeInData.collapsedChildren

        // 5. 重新生成图数据并更新
        try {
          const newGraphData = treeToGraphData(rawData)
          graph.changeData(newGraphData)
        } catch (err) {
          console.error('Failed to update graph data:', err)
        }
      } else {
        console.log('Clicked node:', model.id)
        // 可以添加普通节点点击逻辑
      }
    }

    graph.on('node:click', handleClick)

    // 清理函数
    onBeforeUnmount(() => {
      graph.off('node:click', handleClick)
      if (graph) {
        graph.destroy()
        graph = null
      }
    })
  } catch (error) {
    handleError(error)
  }
}

// 组件挂载后初始化
onMounted(() => {
  initGraph()
})

// 可选：监听窗口大小变化
window.addEventListener('resize', () => {
  graph?.fitView()
})
</script>

<style scoped>
.graph-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #f8f9fa;
}
</style>
