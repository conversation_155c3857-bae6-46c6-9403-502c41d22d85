<script setup lang="jsx">
import { ref } from 'vue'
import BlockHeader from '@/components/BlockHeader.vue'
import { BrowseIcon, BrowseOffIcon, HelpCircleFilledIcon } from 'tdesign-icons-vue-next'

defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

const hidePublic = ref(true)
const hidePrivate = ref(true)
</script>

<template>
  <div class="application-detail-page">
    <section>
      <BlockHeader title="基础信息" style="margin-bottom: 20px"></BlockHeader>
      <t-row :gutter="[12, 12]">
        <t-col class="flex-align-center" :span="4">
          <span class="label">应用ID</span>
          <span class="content">202502250002</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">所属应用</span>
          <span class="content">916619319350666</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">应用描述</span>
          <span class="content">111</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">管理员</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">操作人</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">应用加密算法</span>
          <span class="content">kkk</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">密钥位数</span>
          <span class="content">kkk</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">
            加密公钥
            <HelpCircleFilledIcon />
          </span>
          <span class="content">
            {{ hidePublic ? '********' : '1234567890' }}
            <BrowseIcon class="eye-icon" @click="hidePublic = !hidePublic" v-if="hidePublic" />
            <BrowseOffIcon class="eye-icon" @click="hidePublic = !hidePublic" v-else />
          </span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">加密私钥</span>
          <span class="content">
            {{ hidePrivate ? '********' : '1234567890' }}
            <BrowseIcon class="eye-icon" @click="hidePrivate = !hidePrivate" v-if="hidePrivate" />
            <BrowseOffIcon class="eye-icon" @click="hidePrivate = !hidePrivate" v-else />
          </span>
        </t-col>
      </t-row>
    </section>
  </div>
</template>

<style lang="less" scoped>
.application-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .label {
    display: inline-block;
    width: 92px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~'100% - 100px');
  }
  .eye-icon {
    cursor: pointer;
  }
}
</style>
