<script setup lang="jsx">
import BlockHeader from '@/components/BlockHeader.vue'

defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})
</script>

<template>
  <div class="field-detail-page">
    <section>
      <BlockHeader title="基础信息" style="margin-bottom: 20px"></BlockHeader>
      <t-row :gutter="[12, 12]">
        <t-col class="flex-align-center" :span="4">
          <span class="label">字段名</span>
          <span class="content">202502250002</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">字段类型</span>
          <span class="content">916619319350666</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">所属数据表</span>
          <span class="content">111</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">描述</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">最近更新时间</span>
          <span class="content">2025-01-06 19:19:41</span>
        </t-col>
        <t-col class="flex-align-center" :span="4">
          <span class="label">更新人</span>
          <span class="content">kkk</span>
        </t-col>
      </t-row>
    </section>
  </div>
</template>

<style lang="less" scoped>
.field-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .label {
    display: inline-block;
    width: 92px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~'100% - 100px');
  }
}
</style>
